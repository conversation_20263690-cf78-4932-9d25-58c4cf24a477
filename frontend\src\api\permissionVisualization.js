import request from './request'

export const permissionVisualizationApi = {
  // 获取权限继承关系树
  getInheritanceTree(params) {
    return request.get('/permission-visualization/inheritance-tree', { params })
  },

  // 获取权限继承路径
  getInheritancePath(params) {
    return request.get('/permission-visualization/inheritance-path', { params })
  },

  // 检测权限冲突
  detectConflicts(data) {
    return request.post('/permission-visualization/detect-conflicts', data)
  },

  // 获取权限矩阵
  getPermissionMatrix(params) {
    return request.get('/permission-visualization/permission-matrix', { params })
  },

  // 计算有效权限
  calculateEffectivePermissions(params) {
    return request.get('/permission-visualization/effective-permissions', { params })
  }
}

export const permissionTemplateApi = {
  // 获取权限模板列表
  getList(params) {
    return request.get('/permission-templates', { params })
  },

  // 获取权限模板详情
  getDetail(id) {
    return request.get(`/permission-templates/${id}`)
  },

  // 创建权限模板
  create(data) {
    return request.post('/permission-templates', data)
  },

  // 更新权限模板
  update(id, data) {
    return request.put(`/permission-templates/${id}`, data)
  },

  // 删除权限模板
  delete(id) {
    return request.delete(`/permission-templates/${id}`)
  },

  // 获取推荐模板
  getRecommended(params) {
    return request.get('/permission-templates/recommended', { params })
  },

  // 应用模板到角色
  applyToRole(id, data) {
    return request.post(`/permission-templates/${id}/apply-to-role`, data)
  },

  // 应用模板到用户
  applyToUser(id, data) {
    return request.post(`/permission-templates/${id}/apply-to-user`, data)
  },

  // 复制模板
  duplicate(id, data) {
    return request.post(`/permission-templates/${id}/duplicate`, data)
  }
}

export const permissionAuditApi = {
  // 获取审计日志列表
  getLogs(params) {
    return request.get('/permission-audit/logs', { params })
  },

  // 获取审计日志详情
  getLogDetail(id) {
    return request.get(`/permission-audit/logs/${id}`)
  },

  // 获取用户操作统计
  getUserStats(params) {
    return request.get('/permission-audit/user-stats', { params })
  },

  // 获取组织权限变更统计
  getOrganizationStats(params) {
    return request.get('/permission-audit/organization-stats', { params })
  },

  // 获取权限热点分析
  getPermissionHotspots(params) {
    return request.get('/permission-audit/permission-hotspots', { params })
  },

  // 获取权限冲突列表
  getConflicts(params) {
    return request.get('/permission-audit/conflicts', { params })
  },

  // 获取冲突统计
  getConflictStats() {
    return request.get('/permission-audit/conflict-stats')
  },

  // 解决权限冲突
  resolveConflict(id, data) {
    return request.post(`/permission-audit/conflicts/${id}/resolve`, data)
  },

  // 忽略权限冲突
  ignoreConflict(id, data) {
    return request.post(`/permission-audit/conflicts/${id}/ignore`, data)
  },

  // 导出审计日志
  export(data) {
    return request.post('/permission-audit/export', data)
  }
}
