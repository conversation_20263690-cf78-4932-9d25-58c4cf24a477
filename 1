我正在开发一个实验教学管理系统，已经完成了前两个功能：
1. ✅ 学校信息批量导入功能 - 已完成
2. ✅ 学区划分管理功能 - 已完成

现在需要开发第三个功能：组织权限设置可视化界面

项目位置：f:\xampp\htdocs\gcqets
- 后端：Laravel (backend目录)
- 前端：Vue3 + Element Plus (frontend目录)
- 数据库：MySQL

请帮我继续开发第三个功能：实现组织权限设置的可视化界面。

项目已实现五级权限体系（省/市/区县/学区/学校）
已有完整的用户认证和权限管理基础
菜单已添加，第三个功能显示为"🚧 开发中"状态
当前任务列表状态：
- [x] 实现学校信息批量导入功能 - 已完成
- [x] 实现学区划分管理功能 - 已完成  
- [/] 实现组织权限设置可视化界面 - 进行中

需要实现的具体功能：
1. 权限继承关系可视化展示
2. 权限矩阵管理界面
3. 权限撤销设置功能
4. 权限变更历史和审计
技术栈：
- 后端：Laravel 12.x + PHP 8.2
- 前端：Vue 3 + Element Plus + Vite
- 数据库：MySQL
- 已有模型：User, Organization, Role, Permission
- 已有权限中间件和数据访问控制
开发环境：
- 后端服务：http://localhost:8000 (php artisan serve)
- 前端服务：http://localhost:5174 (npm run dev)
- 数据库已配置并运行
我正在开发实验教学管理系统的第三个功能模块。

项目背景：
- 位置：f:\xampp\htdocs\gcqets
- 技术栈：Laravel + Vue3 + Element Plus + MySQL
- 已完成：批量导入功能、学区划分管理功能
- 当前任务：实现组织权限设置可视化界面

需要开发的功能：
1. 五级组织权限继承关系的可视化展示
2. 权限矩阵管理界面
3. 权限撤销和特殊授权功能
4. 权限变更历史和审计功能

请帮我开始实现这个功能，从数据模型设计开始。
目前已完成：

✅ 功能一：学校信息批量导入（包含Excel/CSV导入、验证、日志）
✅ 功能二：学区划分管理（包含地理信息、自动划分、手动调整、历史记录）
🚧 功能三：组织权限设置可视化界面（待开发）