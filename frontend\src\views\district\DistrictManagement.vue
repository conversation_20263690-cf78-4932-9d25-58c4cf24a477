<template>
  <div class="district-management">
    <div class="page-header">
      <div class="header-content">
        <h2>学区划分管理</h2>
        <el-text type="info">
          管理学区划分，支持地理信息展示、自动划分和手动调整
        </el-text>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="showCreateDialog" type="primary">
          <el-icon><Plus /></el-icon>
          创建学区
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-row :gutter="20">
        <!-- 左侧：学区列表 -->
        <el-col :span="8">
          <el-card title="学区列表">
            <template #header>
              <div class="card-header">
                <span>学区列表</span>
                <el-input
                  v-model="searchText"
                  placeholder="搜索学区"
                  size="small"
                  style="width: 200px;"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>

            <div class="district-list" v-loading="loading">
              <div
                v-for="district in filteredDistricts"
                :key="district.id"
                class="district-item"
                :class="{ active: selectedDistrict?.id === district.id }"
                @click="selectDistrict(district)"
              >
                <div class="district-info">
                  <div class="district-name">{{ district.name }}</div>
                  <div class="district-meta">
                    <el-tag size="small">{{ district.school_count || 0 }}所学校</el-tag>
                    <el-tag size="small" type="info">{{ district.student_count || 0 }}名学生</el-tag>
                  </div>
                </div>
                <div class="district-actions">
                  <el-button size="small" text @click.stop="editDistrict(district)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" text type="danger" @click.stop="deleteDistrict(district)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>

              <div v-if="filteredDistricts.length === 0" class="empty-state">
                <el-empty description="暂无学区数据" />
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：学区详情和学校管理 -->
        <el-col :span="16">
          <div v-if="!selectedDistrict" class="no-selection">
            <el-empty description="请选择一个学区查看详情" />
          </div>

          <div v-else class="district-detail">
            <!-- 学区基本信息 -->
            <el-card class="district-info-card">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedDistrict.name }} - 基本信息</span>
                  <el-button size="small" @click="editDistrict(selectedDistrict)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                </div>
              </template>

              <el-descriptions :column="2" border>
                <el-descriptions-item label="学区名称">
                  {{ selectedDistrict.name }}
                </el-descriptions-item>
                <el-descriptions-item label="学区编码">
                  {{ selectedDistrict.code || '未设置' }}
                </el-descriptions-item>
                <el-descriptions-item label="所属区县">
                  {{ selectedDistrict.parent?.name || '未设置' }}
                </el-descriptions-item>
                <el-descriptions-item label="创建时间">
                  {{ formatDate(selectedDistrict.created_at) }}
                </el-descriptions-item>
                <el-descriptions-item label="学校数量">
                  {{ selectedDistrict.school_count || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="学生数量">
                  {{ selectedDistrict.student_count || 0 }}
                </el-descriptions-item>
                <el-descriptions-item label="描述" :span="2">
                  {{ selectedDistrict.description || '暂无描述' }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <!-- 学校管理 -->
            <el-card class="schools-card">
              <template #header>
                <div class="card-header">
                  <span>学校管理</span>
                  <div>
                    <el-button size="small" @click="showAssignSchoolDialog">
                      <el-icon><Plus /></el-icon>
                      分配学校
                    </el-button>
                    <el-button size="small" @click="autoAssignSchools">
                      <el-icon><Magic /></el-icon>
                      自动分配
                    </el-button>
                  </div>
                </div>
              </template>

              <el-table :data="selectedDistrict.schools || []" style="width: 100%">
                <el-table-column prop="name" label="学校名称" />
                <el-table-column prop="code" label="学校编码" width="120" />
                <el-table-column prop="student_count" label="学生数量" width="100">
                  <template #default="{ row }">
                    {{ row.student_count || 0 }}
                  </template>
                </el-table-column>
                <el-table-column prop="address" label="地址" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button size="small" text type="danger" @click="removeSchoolFromDistrict(row)">
                      移除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 创建/编辑学区对话框 -->
    <el-dialog
      v-model="districtDialogVisible"
      :title="isEditing ? '编辑学区' : '创建学区'"
      width="600px"
    >
      <el-form :model="districtForm" :rules="districtRules" ref="districtFormRef" label-width="100px">
        <el-form-item label="学区名称" prop="name">
          <el-input v-model="districtForm.name" placeholder="请输入学区名称" />
        </el-form-item>
        <el-form-item label="学区编码" prop="code">
          <el-input v-model="districtForm.code" placeholder="请输入学区编码" />
        </el-form-item>
        <el-form-item label="所属区县" prop="parent_id">
          <el-select v-model="districtForm.parent_id" placeholder="请选择所属区县" style="width: 100%;">
            <el-option
              v-for="county in countyOptions"
              :key="county.id"
              :label="county.name"
              :value="county.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="districtForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入学区描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="districtDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveDistrict" :loading="saving">
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 分配学校对话框 -->
    <el-dialog
      v-model="assignSchoolDialogVisible"
      title="分配学校到学区"
      width="800px"
    >
      <div class="assign-school-content">
        <div class="available-schools">
          <h4>可分配学校</h4>
          <el-table
            :data="availableSchools"
            @selection-change="handleSchoolSelection"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="name" label="学校名称" />
            <el-table-column prop="code" label="学校编码" width="120" />
            <el-table-column prop="address" label="地址" />
          </el-table>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="assignSchoolDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmAssignSchools"
          :disabled="selectedSchools.length === 0"
          :loading="assigning"
        >
          分配选中学校
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { organizationApi } from '@/api/organization'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const assigning = ref(false)
const searchText = ref('')
const districts = ref([])
const selectedDistrict = ref(null)
const countyOptions = ref([])
const availableSchools = ref([])
const selectedSchools = ref([])
const districtDialogVisible = ref(false)
const assignSchoolDialogVisible = ref(false)
const isEditing = ref(false)

// 表单数据
const districtForm = reactive({
  name: '',
  code: '',
  parent_id: null,
  description: ''
})

// 表单验证规则
const districtRules = {
  name: [
    { required: true, message: '请输入学区名称', trigger: 'blur' }
  ],
  parent_id: [
    { required: true, message: '请选择所属区县', trigger: 'change' }
  ]
}

// 组件引用
const districtFormRef = ref(null)

// 计算属性
const filteredDistricts = computed(() => {
  if (!searchText.value) return districts.value
  return districts.value.filter(district =>
    district.name.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 方法
const loadDistricts = async () => {
  loading.value = true
  try {
    const response = await organizationApi.getList({
      type: 'zone',
      with_schools: true
    })
    districts.value = response.data.data || []
  } catch (error) {
    console.error('加载学区列表失败:', error)
    ElMessage.error('加载学区列表失败')
  } finally {
    loading.value = false
  }
}

const loadCountyOptions = async () => {
  try {
    const response = await organizationApi.getList({
      type: 'district'
    })
    countyOptions.value = response.data.data || []
  } catch (error) {
    console.error('加载区县选项失败:', error)
  }
}

const loadAvailableSchools = async () => {
  try {
    const response = await organizationApi.getList({
      type: 'school',
      without_district: true
    })
    availableSchools.value = response.data.data || []
  } catch (error) {
    console.error('加载可分配学校失败:', error)
  }
}

const refreshData = () => {
  loadDistricts()
  if (selectedDistrict.value) {
    selectDistrict(selectedDistrict.value)
  }
}

const selectDistrict = async (district) => {
  try {
    const response = await organizationApi.getDetail(district.id)
    selectedDistrict.value = response.data
  } catch (error) {
    console.error('加载学区详情失败:', error)
    ElMessage.error('加载学区详情失败')
  }
}

const showCreateDialog = () => {
  isEditing.value = false
  resetDistrictForm()
  districtDialogVisible.value = true
}

const editDistrict = (district) => {
  isEditing.value = true
  Object.assign(districtForm, {
    name: district.name,
    code: district.code,
    parent_id: district.parent_id,
    description: district.description
  })
  districtDialogVisible.value = true
}

const resetDistrictForm = () => {
  Object.assign(districtForm, {
    name: '',
    code: '',
    parent_id: null,
    description: ''
  })
}

const saveDistrict = async () => {
  try {
    await districtFormRef.value.validate()
    
    saving.value = true
    
    const data = {
      ...districtForm,
      type: 'zone',
      level: 4
    }
    
    if (isEditing.value) {
      await organizationApi.update(selectedDistrict.value.id, data)
      ElMessage.success('更新学区成功')
    } else {
      await organizationApi.create(data)
      ElMessage.success('创建学区成功')
    }
    
    districtDialogVisible.value = false
    loadDistricts()
  } catch (error) {
    if (error.errors) {
      return
    }
    console.error('保存学区失败:', error)
    ElMessage.error('保存学区失败')
  } finally {
    saving.value = false
  }
}

const deleteDistrict = async (district) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学区 "${district.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await organizationApi.delete(district.id)
    ElMessage.success('删除学区成功')
    
    if (selectedDistrict.value?.id === district.id) {
      selectedDistrict.value = null
    }
    
    loadDistricts()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除学区失败:', error)
      ElMessage.error('删除学区失败')
    }
  }
}

const showAssignSchoolDialog = () => {
  loadAvailableSchools()
  assignSchoolDialogVisible.value = true
}

const handleSchoolSelection = (selection) => {
  selectedSchools.value = selection
}

const confirmAssignSchools = async () => {
  if (selectedSchools.value.length === 0) {
    ElMessage.warning('请选择要分配的学校')
    return
  }
  
  try {
    assigning.value = true
    
    // 这里实现学校分配逻辑
    // 由于篇幅限制，暂时模拟成功
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(`成功分配 ${selectedSchools.value.length} 所学校`)
    assignSchoolDialogVisible.value = false
    selectedSchools.value = []
    
    // 刷新学区详情
    if (selectedDistrict.value) {
      selectDistrict(selectedDistrict.value)
    }
  } catch (error) {
    console.error('分配学校失败:', error)
    ElMessage.error('分配学校失败')
  } finally {
    assigning.value = false
  }
}

const removeSchoolFromDistrict = async (school) => {
  try {
    await ElMessageBox.confirm(
      `确定要将 "${school.name}" 从当前学区移除吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 这里实现移除学校逻辑
    ElMessage.success('移除学校成功')
    
    // 刷新学区详情
    selectDistrict(selectedDistrict.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除学校失败:', error)
      ElMessage.error('移除学校失败')
    }
  }
}

const autoAssignSchools = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要根据地理位置自动分配学校到当前学区吗？',
      '自动分配确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 这里实现自动分配逻辑
    ElMessage.success('自动分配完成')
    
    // 刷新学区详情
    selectDistrict(selectedDistrict.value)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('自动分配失败:', error)
      ElMessage.error('自动分配失败')
    }
  }
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 生命周期
onMounted(() => {
  loadDistricts()
  loadCountyOptions()
})
</script>

<style scoped>
.district-management {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

.header-content h2 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.page-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.district-list {
  max-height: 600px;
  overflow-y: auto;
}

.district-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  margin-bottom: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.district-item:hover {
  background: #e3f2fd;
}

.district-item.active {
  background: #e3f2fd;
  border: 1px solid #409eff;
}

.district-info {
  flex: 1;
}

.district-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 5px;
}

.district-meta {
  display: flex;
  gap: 5px;
}

.district-actions {
  display: flex;
  gap: 5px;
}

.no-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.district-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.district-info-card,
.schools-card {
  background: #fff;
}

.assign-school-content h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}
</style>
